import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Target,
    MessageCircle,
    Handshake
} from 'lucide-react';

interface WhyBsgItem {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    gradient: string;
}

const whyBsgItems: WhyBsgItem[] = [
    {
        icon: Target,
        title: 'Results That Matter',
        description: 'We\'re driven by outcomes—delivering measurable impact aligned with your business goals.',
        gradient: 'from-blue-500 to-blue-600'
    },
    {
        icon: MessageCircle,
        title: 'Crystal-Clear Communication',
        description: 'Transparent, timely, and consistent communication from start to finish.',
        gradient: 'from-green-500 to-green-600'
    },
    {
        icon: Handshake,
        title: 'Ongoing Partnership',
        description: 'Our support continues beyond launch, evolving with your business needs.',
        gradient: 'from-purple-500 to-purple-600'
    }
];

export function WhyBsgSection() {
    return (
        <section className="py-16 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why BSG Support?</h2>
                    <p className="text-xl text-gray-600">We go beyond standard support to deliver exceptional value</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {whyBsgItems.map((item, index) => (
                        <WhyBsgCard key={item.title} item={item} index={index} />
                    ))}
                </div>
            </div>
        </section>
    );
}

interface WhyBsgCardProps {
    item: WhyBsgItem;
    index: number;
}

function WhyBsgCard({ item, index }: WhyBsgCardProps) {
    const Icon = item.icon;
    
    return (
        <Card className="text-center group hover:shadow-lg transition-all duration-300 border-gray-200 hover:border-blue-200">
            <CardHeader>
                <div className={`w-16 h-16 bg-gradient-to-br ${item.gradient} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform`}>
                    <Icon className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-2xl mb-4 text-gray-900">{item.title}</CardTitle>
                <CardDescription className="text-gray-600 text-base leading-relaxed">
                    {item.description}
                </CardDescription>
            </CardHeader>
        </Card>
    );
}
