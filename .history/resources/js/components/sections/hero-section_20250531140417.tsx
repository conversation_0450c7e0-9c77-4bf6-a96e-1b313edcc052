import { Link, usePage } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { type SharedData } from '@/types';
import {
    ArrowRight,
    BarChart3,
    TrendingUp,
    Users,
    CheckCircle,
    Clock,
    Shield,
    Globe
} from 'lucide-react';

export function HeroSection() {
    const { auth } = usePage<SharedData>().props;

    return (
        <section className="pt-24 pb-16 bg-gradient-to-br from-primary/5 via-white to-secondary/5 overflow-hidden">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    {/* Left Content */}
                    <div className="text-center lg:text-left">
                        <Badge variant="outline" className="mb-6 text-primary border-primary/20 bg-white/50">
                            Trust Us as Your Strategic Partner
                        </Badge>
                        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                            Scale Smarter. <span className="text-primary">Grow Faster.</span>
                        </h1>
                        <p className="text-xl text-gray-700 mb-4 font-medium">
                            with <span className="font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">Backsure Global Support</span>
                        </p>
                        <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                            In today's competitive landscape, success requires a new approach. At BSG Support, we don't follow the usual path — we redefine it.
                            We challenge business-as-usual through insight, innovation, and real, measurable outcomes.
                        </p>
                        <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                            Our tailored support solutions are designed to reduce risk, control costs, and drive growth — especially in the dynamic world of (re)insurance and financial services.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start items-center">
                            <Button size="lg" className="bg-primary hover:bg-primary/90 text-white px-8 py-3 shadow-lg hover:shadow-xl transition-all" asChild>
                                <Link href="/contact">
                                    Talk to Our Team
                                    <ArrowRight className="ml-2 h-5 w-5" />
                                </Link>
                            </Button>
                            <p className="text-sm text-gray-500">Let's build a better way forward, together.</p>
                        </div>
                    </div>

                    {/* Right Image */}
                    <div className="relative lg:order-last">
                        <div className="relative">
                            {/* Background decorative elements */}
                            <div className="absolute -top-4 -right-4 w-72 h-72 bg-blue-100 rounded-full opacity-20 blur-3xl"></div>
                            <div className="absolute -bottom-8 -left-8 w-64 h-64 bg-purple-100 rounded-full opacity-20 blur-3xl"></div>

                            {/* Main illustration container */}
                            <div className="relative bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                                {/* ERP Dashboard Mockup */}
                                <div className="space-y-4">
                                    {/* Header */}
                                    <div className="flex items-center justify-between pb-4 border-b border-gray-200">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                                <BarChart3 className="h-5 w-5 text-white" />
                                            </div>
                                            <span className="font-semibold text-gray-900">ERP Dashboard</span>
                                        </div>
                                        <div className="flex space-x-2">
                                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                                        </div>
                                    </div>

                                    {/* Stats Cards */}
                                    <div className="grid grid-cols-2 gap-3">
                                        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-3 rounded-lg">
                                            <div className="flex items-center space-x-2">
                                                <TrendingUp className="h-4 w-4 text-blue-600" />
                                                <span className="text-xs font-medium text-blue-900">Revenue</span>
                                            </div>
                                            <div className="text-lg font-bold text-blue-900 mt-1">$2.4M</div>
                                            <div className="text-xs text-blue-600">+12.5%</div>
                                        </div>
                                        <div className="bg-gradient-to-r from-green-50 to-green-100 p-3 rounded-lg">
                                            <div className="flex items-center space-x-2">
                                                <Users className="h-4 w-4 text-green-600" />
                                                <span className="text-xs font-medium text-green-900">Clients</span>
                                            </div>
                                            <div className="text-lg font-bold text-green-900 mt-1">1,247</div>
                                            <div className="text-xs text-green-600">+8.2%</div>
                                        </div>
                                    </div>

                                    {/* Chart representation */}
                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <div className="flex items-center justify-between mb-3">
                                            <span className="text-sm font-medium text-gray-700">Performance Overview</span>
                                            <Badge variant="secondary" className="text-xs">Live</Badge>
                                        </div>
                                        <div className="flex items-end space-x-1 h-16">
                                            {[40, 65, 45, 80, 55, 90, 70, 85].map((height, i) => (
                                                <div
                                                    key={i}
                                                    className="bg-gradient-to-t from-blue-600 to-blue-400 rounded-sm flex-1"
                                                    style={{ height: `${height}%` }}
                                                ></div>
                                            ))}
                                        </div>
                                    </div>

                                    {/* Action items */}
                                    <div className="space-y-2">
                                        <div className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                                            <CheckCircle className="h-4 w-4 text-green-500" />
                                            <span className="text-sm text-gray-700">Process automation active</span>
                                        </div>
                                        <div className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                                            <Clock className="h-4 w-4 text-blue-500" />
                                            <span className="text-sm text-gray-700">24/7 monitoring enabled</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Floating elements */}
                            <div className="absolute -top-6 -left-6 bg-white rounded-lg shadow-lg p-3 border border-gray-200">
                                <div className="flex items-center space-x-2">
                                    <Shield className="h-4 w-4 text-green-500" />
                                    <span className="text-xs font-medium text-gray-700">Secure</span>
                                </div>
                            </div>

                            <div className="absolute -bottom-4 -right-6 bg-white rounded-lg shadow-lg p-3 border border-gray-200">
                                <div className="flex items-center space-x-2">
                                    <Globe className="h-4 w-4 text-blue-500" />
                                    <span className="text-xs font-medium text-gray-700">Global</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
