import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import {
    Target,
    Settings,
    Users,
    Globe,
    Star
} from 'lucide-react';

interface Feature {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
}

const features: Feature[] = [
    {
        icon: Target,
        title: 'Core Service Domains',
        description: 'Supporting businesses across industries'
    },
    {
        icon: Settings,
        title: 'Flexible Contract',
        description: 'Simple terms with transparent monthly billing'
    },
    {
        icon: Users,
        title: 'Tailored Support',
        description: 'Designed to fit startups, SMEs, and large enterprises'
    },
    {
        icon: Globe,
        title: 'Global Support',
        description: 'Always available, wherever you are'
    },
    {
        icon: Star,
        title: 'Positive Client Experiences',
        description: 'Trusted by early clients and growing partnerships'
    }
];

export function FeaturesSection() {
    return (
        <section className="py-16 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Features</h2>
                    <p className="text-xl text-gray-600">Amazing features we offer</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {features.map((feature, index) => (
                        <FeatureCard key={feature.title} feature={feature} index={index} />
                    ))}
                </div>
            </div>
        </section>
    );
}

interface FeatureCardProps {
    feature: Feature;
    index: number;
}

function FeatureCard({ feature, index }: FeatureCardProps) {
    const Icon = feature.icon;

    return (
        <Card className="group hover:shadow-lg transition-all duration-300 border-gray-200 hover:border-primary/20">
            <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors">
                    <Icon className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-xl text-gray-900">{feature.title}</CardTitle>
            </CardHeader>
            <CardContent>
                <CardDescription className="text-gray-600">
                    {feature.description}
                </CardDescription>
            </CardContent>
        </Card>
    );
}
