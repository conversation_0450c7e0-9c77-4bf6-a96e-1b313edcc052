import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FaqItem {
    question: string;
    answer: string;
}

const faqItems: FaqItem[] = [
    {
        question: "What industries do you serve?",
        answer: "We specialize in (re)insurance and financial services, but our expertise extends to various industries including healthcare, technology, and manufacturing."
    },
    {
        question: "How do you ensure data security?",
        answer: "We implement enterprise-grade security measures including encryption, secure access controls, and regular security audits to protect your sensitive data."
    },
    {
        question: "What is your pricing model?",
        answer: "We offer flexible pricing models including dedicated teams, on-demand services, and comprehensive care plans. Contact us for a customized quote based on your specific needs."
    },
    {
        question: "How quickly can you get started?",
        answer: "Depending on your requirements, we can typically begin within 1-2 weeks after initial consultation and agreement on scope and terms."
    },
    {
        question: "Do you provide 24/7 support?",
        answer: "Yes, we offer 24/7 support for our Business Care Plans and On-Demand Service Support customers. Support levels vary by service tier."
    },
    {
        question: "Can you scale services up or down based on our needs?",
        answer: "Absolutely! Our flexible service model allows you to scale resources up or down based on your changing business requirements."
    },
    {
        question: "What happens if we need to integrate your services with our existing enterprise systems and workflows while maintaining compliance with industry regulations?",
        answer: "We have extensive experience integrating with various enterprise systems including ERP, CRM, and specialized industry software. Our team works closely with your IT department to ensure seamless integration while maintaining all necessary compliance standards and security protocols."
    }
];

export function FaqSection() {
    const [openFaq, setOpenFaq] = useState<number | null>(null);

    return (
        <section className="py-16 bg-gray-50 w-full overflow-hidden">
            <div className="max-w-4xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 w-full">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                    <p className="text-xl text-gray-600">Have Questions? We've Got Answers. A quick look at some common queries about Backsure's solutions and services.</p>
                </div>

                <div className="space-y-4 w-full">
                    {faqItems.map((faq, index) => (
                        <FaqItem
                            key={index}
                            faq={faq}
                            isOpen={openFaq === index}
                            onToggle={() => setOpenFaq(openFaq === index ? null : index)}
                        />
                    ))}
                </div>
            </div>
        </section>
    );
}

interface FaqItemProps {
    faq: FaqItem;
    index: number;
    isOpen: boolean;
    onToggle: () => void;
}

function FaqItem({ faq, isOpen, onToggle }: Omit<FaqItemProps, 'index'>) {
    return (
        <div className="w-full">
            <Collapsible open={isOpen} onOpenChange={onToggle} className="w-full">
                <CollapsibleTrigger asChild>
                    <Button
                        variant="ghost"
                        className="w-full h-auto text-left bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-0 overflow-hidden"
                    >
                        <div className="flex items-start justify-between w-full p-4 sm:p-6 gap-3 sm:gap-4 min-w-0">
                            <span className="text-base sm:text-lg font-semibold text-gray-900 text-left leading-relaxed flex-1 min-w-0 break-words hyphens-auto max-w-9">
                                {faq.question}
                            </span>
                            <ChevronDown
                                className={cn(
                                    "h-5 w-5 text-gray-500 transition-transform flex-shrink-0 mt-0.5",
                                    isOpen && "rotate-180"
                                )}
                            />
                        </div>
                    </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="w-full">
                    <div className="px-4 sm:px-6 pb-4 sm:pb-6 bg-white border-l border-r border-b border-gray-200 rounded-b-lg">
                        <p className="text-gray-600 leading-relaxed break-words hyphens-auto">{faq.answer}</p>
                    </div>
                </CollapsibleContent>
            </Collapsible>
        </div>
    );
}
