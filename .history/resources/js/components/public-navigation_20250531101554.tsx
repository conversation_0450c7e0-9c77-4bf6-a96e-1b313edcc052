"use client"

import * as React from "react"
import { useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    NavigationMenuTrigger,
    navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import {
    Sheet,
    SheetContent,
    SheetTrigger,
} from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { type SharedData } from '@/types';
import { mainNavigation, rightNavigation } from '@/data/navigation';
import { Menu, ChevronDown } from 'lucide-react';

interface PublicNavigationProps {
    className?: string;
}

export function PublicNavigation({ className }: PublicNavigationProps) {
    const { auth } = usePage<SharedData>().props;
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    return (
        <nav className={cn("fixed top-0 w-full bg-white/95 backdrop-blur-md border-b border-gray-200 z-50", className)}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    {/* Logo */}
                    <div className="flex items-center">
                        <Link href="/" className="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                            BSG Support
                        </Link>
                    </div>

                    {/* Desktop Navigation */}
                    <div className="hidden lg:flex items-center space-x-1">
                        <NavigationMenu className="relative">
                            <NavigationMenuList>
                                {mainNavigation.map((item) => (
                                    <NavigationMenuItem key={item.title}>
                                        {item.children ? (
                                            <>
                                                <NavigationMenuTrigger className="text-gray-700 hover:text-blue-600 transition-colors">
                                                    {item.title}
                                                </NavigationMenuTrigger>
                                                <NavigationMenuContent>
                                                    <ul className="grid gap-3 p-6 w-[400px] lg:w-[500px] lg:grid-cols-2">
                                                        {item.children.map((child) => (
                                                            <ListItem
                                                                key={child.title}
                                                                title={child.title}
                                                                href={child.href}
                                                                icon={child.icon}
                                                            >
                                                                {child.description}
                                                            </ListItem>
                                                        ))}
                                                    </ul>
                                                </NavigationMenuContent>
                                            </>
                                        ) : (
                                            <NavigationMenuLink asChild className={navigationMenuTriggerStyle()}>
                                                <Link href={item.href} className="text-gray-700 hover:text-blue-600">
                                                    {item.title}
                                                </Link>
                                            </NavigationMenuLink>
                                        )}
                                    </NavigationMenuItem>
                                ))}
                            </NavigationMenuList>
                        </NavigationMenu>
                    </div>

                    {/* Right Side Navigation */}
                    <div className="hidden lg:flex items-center gap-4">
                        {rightNavigation.map((item) => (
                            <Button key={item.title} variant="ghost" asChild>
                                <Link href={item.href} className="text-gray-700 hover:text-blue-600">
                                    {item.title}
                                </Link>
                            </Button>
                        ))}

                        {/* {auth.user ? (
                            <Button asChild variant="default">
                                <Link href="/dashboard">Dashboard</Link>
                            </Button>
                        ) : (
                            <div className="flex items-center gap-2">
                                <Button asChild variant="ghost">
                                    <Link href="/login">Log in</Link>
                                </Button>
                                <Button asChild>
                                    <Link href="/register">Register</Link>
                                </Button>
                            </div>
                        )} */}
                    </div>

                    {/* Mobile Menu Button */}
                    <div className="lg:hidden">
                        <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
                            <SheetTrigger asChild>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-10 w-10 text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                                >
                                    <Menu className="h-6 w-6" />
                                    <span className="sr-only">Open menu</span>
                                </Button>
                            </SheetTrigger>
                            <SheetContent side="right" className="w-[300px] sm:w-[350px] p-0">
                                <MobileNavigationContent onClose={() => setMobileMenuOpen(false)} />
                            </SheetContent>
                        </Sheet>
                    </div>
                </div>
            </div>
        </nav>
    );
}

// ListItem component for desktop navigation dropdowns
function ListItem({
    title,
    children,
    href,
    icon: Icon,
    ...props
}: React.ComponentPropsWithoutRef<"li"> & {
    href: string;
    icon?: React.ComponentType<{ className?: string }> | null;
}) {
    return (
        <li {...props}>
            <NavigationMenuLink asChild>
                <Link
                    href={href}
                    className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                >
                    <div className="flex items-center space-x-2">
                        {Icon && <Icon className="h-4 w-4 text-blue-600" />}
                        <div className="text-sm font-medium leading-none">{title}</div>
                    </div>
                    {children && (
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                            {children}
                        </p>
                    )}
                </Link>
            </NavigationMenuLink>
        </li>
    );
}

interface MobileNavigationContentProps {
    onClose: () => void;
}

function MobileNavigationContent({ onClose }: MobileNavigationContentProps) {
    const { auth } = usePage<SharedData>().props;
    const [expandedItems, setExpandedItems] = useState<string[]>([]);

    const toggleExpanded = (title: string) => {
        setExpandedItems(prev =>
            prev.includes(title)
                ? prev.filter(item => item !== title)
                : [...prev, title]
        );
    };

    const handleLinkClick = () => {
        onClose();
    };

    return (
        <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-lg font-semibold">Menu</h2>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-6">
                {/* Main Navigation */}
                <div className="space-y-2">
                    {mainNavigation.map((item) => (
                        <div key={item.title}>
                            {item.children ? (
                                <div className="space-y-1">
                                    <button
                                        onClick={() => toggleExpanded(item.title)}
                                        className="flex items-center justify-between w-full p-3 text-left rounded-lg hover:bg-accent transition-colors"
                                    >
                                        <div className="flex items-center space-x-2">
                                            {item.icon && <item.icon className="h-4 w-4 text-blue-600" />}
                                            <span className="font-medium">{item.title}</span>
                                        </div>
                                        <ChevronDown
                                            className={cn(
                                                "h-4 w-4 transition-transform",
                                                expandedItems.includes(item.title) && "rotate-180"
                                            )}
                                        />
                                    </button>
                                    {expandedItems.includes(item.title) && (
                                        <div className="ml-6 space-y-1">
                                            {item.children.map((child) => (
                                                <Link
                                                    key={child.title}
                                                    href={child.href}
                                                    onClick={handleLinkClick}
                                                    className="flex items-start space-x-3 p-2 rounded-md hover:bg-accent transition-colors"
                                                >
                                                    {child.icon && <child.icon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />}
                                                    <div className="flex-1 min-w-0">
                                                        <div className="text-sm font-medium">{child.title}</div>
                                                        {child.description && (
                                                            <div className="text-xs text-muted-foreground mt-0.5">{child.description}</div>
                                                        )}
                                                    </div>
                                                </Link>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <Link
                                    href={item.href}
                                    onClick={handleLinkClick}
                                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-accent transition-colors"
                                >
                                    {item.icon && <item.icon className="h-4 w-4 text-blue-600 flex-shrink-0" />}
                                    <span className="font-medium">{item.title}</span>
                                </Link>
                            )}
                        </div>
                    ))}
                </div>

                {/* Right Navigation */}
                <div className="border-t mt-6 pt-6 space-y-2">
                    {rightNavigation.map((item) => (
                        <Link
                            key={item.title}
                            href={item.href}
                            onClick={handleLinkClick}
                            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-accent transition-colors"
                        >
                            {item.icon && <item.icon className="h-4 w-4 text-blue-600 flex-shrink-0" />}
                            <span className="font-medium">{item.title}</span>
                        </Link>
                    ))}
                </div>

                {/* Auth Navigation */}
                <div className="border-t mt-6 pt-6 space-y-3">
                    {auth.user ? (
                        <Button asChild className="w-full">
                            <Link href="/dashboard" onClick={handleLinkClick}>
                                Dashboard
                            </Link>
                        </Button>
                    ) : (
                        <div className="space-y-2">
                            <Button asChild variant="outline" className="w-full">
                                <Link href="/login" onClick={handleLinkClick}>
                                    Log in
                                </Link>
                            </Button>
                            <Button asChild className="w-full">
                                <Link href="/register" onClick={handleLinkClick}>
                                    Register
                                </Link>
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
