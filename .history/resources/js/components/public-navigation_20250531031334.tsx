import { useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
    SheetTrigger,
} from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { type NavItem, type SharedData } from '@/types';
import { mainNavigation, rightNavigation, authNavigation } from '@/data/navigation';
import { Menu, X, ChevronDown } from 'lucide-react';

interface PublicNavigationProps {
    className?: string;
}

export function PublicNavigation({ className }: PublicNavigationProps) {
    const { auth } = usePage<SharedData>().props;
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    return (
        <nav className={cn("fixed top-0 w-full bg-white/95 backdrop-blur-md border-b border-gray-200 z-50", className)}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    {/* Logo */}
                    <div className="flex items-center">
                        <Link href="/" className="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                            BSG Support
                        </Link>
                    </div>

                    {/* Desktop Navigation */}
                    <div className="hidden lg:flex items-center space-x-1">
                        <NavigationMenu className="[&_[data-slot=navigation-menu-viewport]]:bg-white [&_[data-slot=navigation-menu-viewport]]:text-gray-900">
                            <NavigationMenuList>
                                {mainNavigation.map((item) => (
                                    <NavigationMenuItem key={item.title}>
                                        {item.children ? (
                                            <>
                                                <NavigationMenuTrigger className="text-gray-700 hover:text-blue-600 transition-colors bg-white hover:bg-gray-50">
                                                    {item.title}
                                                </NavigationMenuTrigger>
                                                <NavigationMenuContent className="bg-white border border-gray-200 shadow-lg">
                                                    <div className="grid gap-3 p-6 w-[400px] lg:w-[500px] lg:grid-cols-2 bg-white">
                                                        {item.children.map((child) => (
                                                            <NavigationMenuLink key={child.title} asChild>
                                                                <Link
                                                                    href={child.href}
                                                                    className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-gray-50 hover:text-gray-900 focus:bg-gray-50 focus:text-gray-900 bg-white"
                                                                >
                                                                    <div className="flex items-center space-x-2">
                                                                        {child.icon && <child.icon className="h-4 w-4 text-blue-600" />}
                                                                        <div className="text-sm font-medium leading-none text-gray-900">{child.title}</div>
                                                                    </div>
                                                                    {child.description && (
                                                                        <p className="line-clamp-2 text-sm leading-snug text-gray-600">
                                                                            {child.description}
                                                                        </p>
                                                                    )}
                                                                </Link>
                                                            </NavigationMenuLink>
                                                        ))}
                                                    </div>
                                                </NavigationMenuContent>
                                            </>
                                        ) : (
                                            <NavigationMenuLink asChild>
                                                <Link
                                                    href={item.href}
                                                    className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-blue-600 focus:bg-gray-50 focus:text-blue-600 focus:outline-none disabled:pointer-events-none disabled:opacity-50"
                                                >
                                                    {item.title}
                                                </Link>
                                            </NavigationMenuLink>
                                        )}
                                    </NavigationMenuItem>
                                ))}
                            </NavigationMenuList>
                        </NavigationMenu>
                    </div>

                    {/* Right Side Navigation */}
                    <div className="hidden lg:flex items-center gap-4">
                        {rightNavigation.map((item) => (
                            <Button key={item.title} variant="ghost" asChild>
                                <Link href={item.href} className="text-gray-700 hover:text-blue-600">
                                    {item.title}
                                </Link>
                            </Button>
                        ))}

                        {auth.user ? (
                            <Button asChild variant="default">
                                <Link href="/dashboard">Dashboard</Link>
                            </Button>
                        ) : (
                            <div className="flex items-center gap-2">
                                <Button asChild variant="ghost">
                                    <Link href="/login">Log in</Link>
                                </Button>
                                <Button asChild>
                                    <Link href="/register">Register</Link>
                                </Button>
                            </div>
                        )}
                    </div>

                    {/* Mobile Menu Button */}
                    <div className="lg:hidden">
                        <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
                            <SheetTrigger asChild>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-10 w-10 text-gray-700 hover:text-blue-600 hover:bg-gray-50 border border-gray-200"
                                >
                                    <Menu className="h-6 w-6" />
                                    <span className="sr-only">Open menu</span>
                                </Button>
                            </SheetTrigger>
                            <SheetContent side="right" className="w-[280px] sm:w-[320px] md:w-[400px] bg-white max-w-[90vw]">
                                <SheetHeader>
                                    <SheetTitle className="text-left text-gray-900">Navigation</SheetTitle>
                                </SheetHeader>
                                <MobileNavigationContent onClose={() => setMobileMenuOpen(false)} />
                            </SheetContent>
                        </Sheet>
                    </div>
                </div>
            </div>
        </nav>
    );
}

interface MobileNavigationContentProps {
    onClose: () => void;
}

function MobileNavigationContent({ onClose }: MobileNavigationContentProps) {
    const { auth } = usePage<SharedData>().props;
    const [expandedItems, setExpandedItems] = useState<string[]>([]);

    const toggleExpanded = (title: string) => {
        setExpandedItems(prev =>
            prev.includes(title)
                ? prev.filter(item => item !== title)
                : [...prev, title]
        );
    };

    const handleLinkClick = () => {
        onClose();
    };

    return (
        <div className="flex flex-col mt-6 bg-white overflow-y-auto max-h-[calc(100vh-120px)]">
            {/* Main Navigation */}
            <div className="space-y-1">
                {mainNavigation.map((item, index) => (
                    <div key={item.title} className="bg-white">
                        {item.children ? (
                            <div className="space-y-0">
                                <button
                                    onClick={() => toggleExpanded(item.title)}
                                    className="flex items-center justify-between w-full p-3 text-left rounded-lg hover:bg-gray-100 transition-colors bg-white text-gray-900"
                                >
                                    <span className="font-medium text-gray-900">{item.title}</span>
                                    <ChevronDown
                                        className={cn(
                                            "h-4 w-4 transition-transform text-gray-600",
                                            expandedItems.includes(item.title) && "rotate-180"
                                        )}
                                    />
                                </button>
                                {expandedItems.includes(item.title) && (
                                    <div className="ml-4 space-y-1 bg-white pb-2">
                                        {item.children.map((child) => (
                                            <Link
                                                key={child.title}
                                                href={child.href}
                                                onClick={handleLinkClick}
                                                className="flex items-start space-x-3 p-2 rounded-md hover:bg-gray-50 transition-colors bg-white"
                                            >
                                                {child.icon && <child.icon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />}
                                                <div className="flex-1 min-w-0">
                                                    <div className="text-sm font-medium text-gray-900 truncate">{child.title}</div>
                                                    {child.description && (
                                                        <div className="text-xs text-gray-500 mt-0.5 line-clamp-2">{child.description}</div>
                                                    )}
                                                </div>
                                            </Link>
                                        ))}
                                    </div>
                                )}
                            </div>
                        ) : (
                            <Link
                                href={item.href}
                                onClick={handleLinkClick}
                                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 transition-colors bg-white"
                            >
                                {item.icon && <item.icon className="h-4 w-4 text-blue-600 flex-shrink-0" />}
                                <span className="font-medium text-gray-900 truncate">{item.title}</span>
                            </Link>
                        )}
                    </div>
                ))}
            </div>

            {/* Right Navigation */}
            <div className="border-t border-gray-200 pt-4 space-y-2 bg-white">
                {rightNavigation.map((item) => (
                    <Link
                        key={item.title}
                        href={item.href}
                        onClick={handleLinkClick}
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 transition-colors bg-white"
                    >
                        {item.icon && <item.icon className="h-4 w-4 text-blue-600" />}
                        <span className="font-medium text-gray-900">{item.title}</span>
                    </Link>
                ))}
            </div>

            {/* Auth Navigation */}
            <div className="border-t border-gray-200 pt-4 space-y-2 bg-white">
                {auth.user ? (
                    <Link
                        href="/dashboard"
                        onClick={handleLinkClick}
                        className="flex items-center justify-center p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Dashboard
                    </Link>
                ) : (
                    <div className="space-y-2 bg-white">
                        <Link
                            href="/login"
                            onClick={handleLinkClick}
                            className="flex items-center justify-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors bg-white text-gray-900"
                        >
                            Log in
                        </Link>
                        <Link
                            href="/register"
                            onClick={handleLinkClick}
                            className="flex items-center justify-center p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            Register
                        </Link>
                    </div>
                )}
            </div>
        </div>
    );
}
