import { Link } from '@inertiajs/react';
import {
    Globe,
    MessageCircle,
    Mail,
    Phone,
    MapPin,
    Linkedin,
    Twitter,
    Facebook
} from 'lucide-react';

interface FooterSection {
    title: string;
    links: Array<{
        title: string;
        href: string;
        external?: boolean;
    }>;
}

const footerSections: FooterSection[] = [
    {
        title: 'Services',
        links: [
            { title: 'Insurance', href: '/services/insurance' },
            { title: 'Finance & Accounting', href: '/services/finance-accounting' },
            { title: 'HR Management', href: '/services/hr-management' },
            { title: 'Compliance & Admin', href: '/services/compliance-admin' }
        ]
    },
    {
        title: 'Solutions',
        links: [
            { title: 'Dedicated Teams', href: '/solutions/dedicated-teams' },
            { title: 'On-Demand Support', href: '/solutions/on-demand' },
            { title: 'Business Care Plans', href: '/solutions/care-plans' },
            { title: 'Consulting', href: '/solutions/consulting' }
        ]
    },
    {
        title: 'Company',
        links: [
            { title: 'About Us', href: '/about' },
            { title: 'Team', href: '/about/team' },
            { title: 'Careers', href: '/about/careers' },
            { title: 'Case Studies', href: '/case-studies' },
            { title: 'Blog', href: '/blog' }
        ]
    },
    {
        title: 'Support',
        links: [
            { title: 'Contact Us', href: '/contact' },
            { title: 'FAQ', href: '/about/faq' },
            { title: 'Documentation', href: '/docs' },
            { title: 'Data Security', href: '/data-security' },
            { title: 'Privacy Policy', href: '/privacy' }
        ]
    }
];

const socialLinks = [
    { icon: Linkedin, href: '#', label: 'LinkedIn' },
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: Facebook, href: '#', label: 'Facebook' },
    { icon: Globe, href: '#', label: 'Website' }
];

export function PublicFooter() {
    return (
        <footer className="bg-gray-900 text-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                {/* Main Footer Content */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
                    {/* Company Info */}
                    <div className="lg:col-span-2">
                        <div className="text-2xl font-bold text-blue-400 mb-4">BSG Support</div>
                        <p className="text-gray-400 mb-6 max-w-md">
                            Delivering exceptional support solutions for businesses across industries.
                            Scale smarter, grow faster with our strategic partnership.
                        </p>

                        {/* Contact Info */}
                        <div className="space-y-3 mb-6">
                            <div className="flex items-center space-x-3 text-gray-400">
                                <Mail className="h-4 w-4" />
                                <span className="text-sm"><EMAIL></span>
                            </div>
                            <div className="flex items-center space-x-3 text-gray-400">
                                <Phone className="h-4 w-4" />
                                <span className="text-sm">+****************</span>
                            </div>
                            <div className="flex items-center space-x-3 text-gray-400">
                                <MapPin className="h-4 w-4" />
                                <span className="text-sm">Global Operations</span>
                            </div>
                        </div>

                        {/* Social Links */}
                        <div className="flex space-x-4">
                            {socialLinks.map((social) => (
                                <a
                                    key={social.label}
                                    href={social.href}
                                    className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors"
                                    aria-label={social.label}
                                >
                                    <social.icon className="h-5 w-5" />
                                </a>
                            ))}
                        </div>
                    </div>

                    {/* Footer Sections */}
                    {footerSections.map((section) => (
                        <div key={section.title}>
                            <h3 className="text-lg font-semibold mb-4">{section.title}</h3>
                            <ul className="space-y-2">
                                {section.links.map((link) => (
                                    <li key={link.title}>
                                        {link.external ? (
                                            <a
                                                href={link.href}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-gray-400 hover:text-white transition-colors cursor-pointer text-sm"
                                            >
                                                {link.title}
                                            </a>
                                        ) : (
                                            <Link
                                                href={link.href}
                                                className="text-gray-400 hover:text-white transition-colors cursor-pointer text-sm"
                                            >
                                                {link.title}
                                            </Link>
                                        )}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>

                {/* Newsletter Signup */}
                <div className="border-t border-gray-800 mt-12 pt-8">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="mb-4 md:mb-0">
                            <h3 className="text-lg font-semibold mb-2">Stay Updated</h3>
                            <p className="text-gray-400 text-sm">Get the latest insights and updates from BSG Support</p>
                        </div>
                        <div className="flex flex-col sm:flex-row space-x-4">
                            <input
                                type="email"
                                placeholder="Enter your email"
                                className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                            <button className="px-6 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                                Subscribe
                            </button>
                        </div>
                    </div>
                </div>

                {/* Bottom Bar */}
                <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                    <p className="text-gray-400 text-sm mb-4 md:mb-0">
                        &copy; 2024 Backsure Global Support. All rights reserved.
                    </p>
                    <div className="flex space-x-6 text-sm">
                        <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                            Privacy Policy
                        </Link>
                        <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                            Terms of Service
                        </Link>
                        <Link href="/cookies" className="text-gray-400 hover:text-white transition-colors">
                            Cookie Policy
                        </Link>
                    </div>
                </div>
            </div>
        </footer>
    );
}
