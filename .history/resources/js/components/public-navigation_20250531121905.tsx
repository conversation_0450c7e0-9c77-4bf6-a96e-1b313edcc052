"use client"

import * as React from "react"
import { useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    NavigationMenuTrigger,
    navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import {
    Sheet,
    SheetContent,
    SheetTrigger,
} from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { type SharedData } from '@/types';
import { mainNavigation, rightNavigation } from '@/data/navigation';
import { Menu, ChevronDown, X } from 'lucide-react';

interface PublicNavigationProps {
    className?: string;
}

export function PublicNavigation({ className }: PublicNavigationProps) {
    const { auth } = usePage<SharedData>().props;
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    return (
        <nav className={cn("fixed top-0 inset-x-0 bg-white/95 backdrop-blur-md border-b border-gray-200 z-50 w-full", className)}>
            <div className="w-full px-2 sm:px-4 lg:px-8 mx-auto">
                <div className="flex justify-between items-center h-16 relative">
                    {/* Mobile Menu Button (Left) */}
                    <div className="lg:hidden flex items-center -ml-1">
                        <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
                            <SheetTrigger asChild>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-9 w-9 text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                                >
                                    <Menu className="h-5 w-5" />
                                    <span className="sr-only">Open menu</span>
                                </Button>
                            </SheetTrigger>
                            <SheetContent
                                side="top"
                                className="w-full h-[100vh] p-0 max-w-none"
                            >
                                <MobileNavigationContent onClose={() => setMobileMenuOpen(false)} />
                            </SheetContent>
                        </Sheet>
                    </div>

                    {/* Logo */}
                    {/* <div className="flex-1 flex items-center justify-center lg:justify-start lg:flex-initial">
                        <Link href="/" className="text-xl sm:text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                            BSG Support
                        </Link>
                    </div> */}
                    <span>Log</span>

                    {/* Desktop Navigation */}
                    <div className="hidden lg:flex items-center space-x-1 flex-1 justify-center">
                        <NavigationMenu viewport={false}>
                            <NavigationMenuList>
                                {mainNavigation.map((item) => (
                                    <NavigationMenuItem key={item.title}>
                                        {item.children ? (
                                            <>
                                                <NavigationMenuTrigger className="text-gray-700 hover:text-blue-600 transition-colors">
                                                    {item.title}
                                                </NavigationMenuTrigger>
                                                <NavigationMenuContent className="left-1/2 transform -translate-x-1/2">
                                                    <ul className="grid gap-3 p-6 w-[400px] lg:w-[500px] lg:grid-cols-2">
                                                        {item.children.map((child) => (
                                                            <ListItem
                                                                key={child.title}
                                                                title={child.title}
                                                                href={child.href}
                                                                icon={child.icon}
                                                            >
                                                                {child.description}
                                                            </ListItem>
                                                        ))}
                                                    </ul>
                                                </NavigationMenuContent>
                                            </>
                                        ) : (
                                            <NavigationMenuLink asChild className={navigationMenuTriggerStyle()}>
                                                <Link href={item.href} className="text-gray-700 hover:text-blue-600">
                                                    {item.title}
                                                </Link>
                                            </NavigationMenuLink>
                                        )}
                                    </NavigationMenuItem>
                                ))}
                            </NavigationMenuList>
                        </NavigationMenu>
                    </div>

                    {/* Right Side Navigation */}
                    <div className="hidden lg:flex items-center gap-4">
                        {rightNavigation.map((item) => (
                            <Button key={item.title} variant="ghost" asChild>
                                <Link href={item.href} className="text-gray-700 hover:text-blue-600">
                                    {item.title}
                                </Link>
                            </Button>
                        ))}
                    </div>

                    {/* Mobile Right Space (for symmetry) */}
                    <div className="w-9 lg:hidden"></div>
                </div>
            </div>
        </nav>
    );
}

interface MobileNavigationContentProps {
    onClose: () => void;
}

function ListItem({
    title,
    children,
    href,
    icon: Icon,
    ...props
}: React.ComponentPropsWithoutRef<"li"> & {
    href: string;
    icon?: React.ComponentType<{ className?: string }> | null;
}) {
    return (
        <li {...props}>
            <NavigationMenuLink asChild>
                <Link
                    href={href}
                    className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                >
                    <div className="flex items-center space-x-2">
                        {Icon && <Icon className="h-4 w-4 text-blue-600" />}
                        <div className="text-sm font-medium leading-none">{title}</div>
                    </div>
                    {children && (
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                            {children}
                        </p>
                    )}
                </Link>
            </NavigationMenuLink>
        </li>
    );
}

function MobileNavigationContent({ onClose }: MobileNavigationContentProps) {
    const { auth } = usePage<SharedData>().props;
    const [expandedItems, setExpandedItems] = useState<string[]>([]);

    const toggleExpanded = (title: string) => {
        setExpandedItems(prev =>
            prev.includes(title)
                ? prev.filter(item => item !== title)
                : [...prev, title]
        );
    };

    const handleLinkClick = () => {
        onClose();
    };

    return (
        <div className="flex flex-col h-full bg-white">
            {/* Header with Close Button */}
            <div className="sticky top-0 flex items-center justify-between px-2 sm:px-4 h-16 border-b bg-white z-10">
                <Link
                    href="/"
                    onClick={handleLinkClick}
                    className="text-lg sm:text-xl font-bold text-blue-600 hover:text-blue-700 transition-colors"
                >
                    BSG Support
                </Link>
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={onClose}
                    className="h-9 w-9 text-gray-500 hover:text-gray-700"
                >
                    <X className="h-5 w-5" />
                    <span className="sr-only">Close menu</span>
                </Button>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto">
                <div className="w-full px-2 sm:px-4 py-4 space-y-4">
                    {/* Main Navigation */}
                    <div className="space-y-1">
                        {mainNavigation.map((item) => (
                            <div key={item.title}>
                                {item.children ? (
                                    <div className="space-y-1">
                                        <button
                                            onClick={() => toggleExpanded(item.title)}
                                            className="flex items-center justify-between w-full p-3 text-left rounded-lg hover:bg-gray-50 transition-colors"
                                        >
                                            <div className="flex items-center">
                                                {item.icon && <item.icon className="h-5 w-5 text-blue-600 mr-3" />}
                                                <span className="font-medium text-gray-900">{item.title}</span>
                                            </div>
                                            <ChevronDown
                                                className={cn(
                                                    "h-5 w-5 text-gray-500 transition-transform",
                                                    expandedItems.includes(item.title) && "rotate-180"
                                                )}
                                            />
                                        </button>
                                        {expandedItems.includes(item.title) && (
                                            <div className="mt-1 ml-4 pl-4 border-l border-gray-200 space-y-1">
                                                {item.children.map((child) => (
                                                    <Link
                                                        key={child.title}
                                                        href={child.href}
                                                        onClick={handleLinkClick}
                                                        className="flex items-center p-3 text-sm text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors"
                                                    >
                                                        {child.icon && <child.icon className="h-4 w-4 text-blue-600 mr-3 flex-shrink-0" />}
                                                        <div>
                                                            <div className="font-medium text-gray-900">{child.title}</div>
                                                            {child.description && (
                                                                <p className="text-sm text-gray-500 mt-0.5">{child.description}</p>
                                                            )}
                                                        </div>
                                                    </Link>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    <Link
                                        href={item.href}
                                        onClick={handleLinkClick}
                                        className="flex items-center w-full p-3 text-gray-900 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors"
                                    >
                                        {item.icon && <item.icon className="h-5 w-5 text-blue-600 mr-3" />}
                                        <span className="font-medium">{item.title}</span>
                                    </Link>
                                )}
                            </div>
                        ))}
                    </div>

                    {/* Right Navigation */}
                    <div className="border-t border-gray-200 pt-4">
                        {rightNavigation.map((item) => (
                            <Link
                                key={item.title}
                                href={item.href}
                                onClick={handleLinkClick}
                                className="flex items-center w-full p-3 text-gray-900 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors"
                            >
                                {item.icon && <item.icon className="h-5 w-5 text-blue-600 mr-3" />}
                                <span className="font-medium">{item.title}</span>
                            </Link>
                        ))}
                    </div>

                    {/* Auth Navigation */}
                    <div className="border-t border-gray-200 pt-4">
                        {auth.user ? (
                            <Button asChild className="w-full justify-start h-12" variant="outline">
                                <Link href="/dashboard" onClick={handleLinkClick}>
                                    Dashboard
                                </Link>
                            </Button>
                        ) : (
                            <div className="space-y-3">
                                <Button asChild className="w-full justify-start h-12" variant="outline">
                                    <Link href="/login" onClick={handleLinkClick}>
                                        Log in
                                    </Link>
                                </Button>
                                <Button asChild className="w-full justify-start h-12">
                                    <Link href="/register" onClick={handleLinkClick}>
                                        Register
                                    </Link>
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
