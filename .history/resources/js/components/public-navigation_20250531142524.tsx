'use client';

import { Button } from '@/components/ui/button';
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    NavigationMenuTrigger,
    navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { mainNavigation, rightNavigation } from '@/data/navigation';
import { cn } from '@/lib/utils';
import { type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { ChevronDown, Menu } from 'lucide-react';
import * as React from 'react';
import { useState } from 'react';
import logoImage from '/public/img/logo.png';

interface PublicNavigationProps {
    className?: string;
}

export function PublicNavigation({ className }: PublicNavigationProps) {
    const { auth } = usePage<SharedData>().props;
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    return (
        <nav className={cn('fixed top-0 z-50 w-full border-b border-primary/20 bg-primary/95 backdrop-blur-md', className)}>
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="flex h-16 items-center justify-between">
                    {/* Logo */}
                    <div className="flex items-center">
                        <Link href="/" className="flex items-center space-x-2 transition-colors hover:opacity-80">
                            <img src={logoImage} alt="BSG Support" className="h-8 w-auto" />
                        </Link>
                    </div>

                    {/* Desktop Navigation */}
                    <div className="hidden items-center space-x-1 lg:flex">
                        <NavigationMenu viewport={false}>
                            <NavigationMenuList>
                                {mainNavigation.map((item) => (
                                    <NavigationMenuItem key={item.title}>
                                        {item.children ? (
                                            <>
                                                <NavigationMenuTrigger className="text-gray-700 transition-colors hover:text-primary">
                                                    {item.title}
                                                </NavigationMenuTrigger>
                                                <NavigationMenuContent className="left-1/2 -translate-x-1/2 transform">
                                                    <ul className="grid w-[400px] gap-3 p-6 lg:w-[500px] lg:grid-cols-2">
                                                        {item.children.map((child) => (
                                                            <ListItem key={child.title} title={child.title} href={child.href} icon={child.icon}>
                                                                {child.description}
                                                            </ListItem>
                                                        ))}
                                                    </ul>
                                                </NavigationMenuContent>
                                            </>
                                        ) : (
                                            <NavigationMenuLink asChild className={navigationMenuTriggerStyle()}>
                                                <Link href={item.href} className="text-gray-700 hover:text-primary">
                                                    {item.title}
                                                </Link>
                                            </NavigationMenuLink>
                                        )}
                                    </NavigationMenuItem>
                                ))}
                            </NavigationMenuList>
                        </NavigationMenu>
                    </div>

                    {/* Right Side Navigation */}
                    <div className="hidden items-center gap-4 lg:flex">
                        {rightNavigation.map((item) => (
                            <Button key={item.title} variant="ghost" asChild>
                                <Link href={item.href} className="text-gray-700 hover:text-primary">
                                    {item.title}
                                </Link>
                            </Button>
                        ))}

                        {/* {auth.user ? (
                            <Button asChild variant="default">
                                <Link href="/dashboard">Dashboard</Link>
                            </Button>
                        ) : (
                            <div className="flex items-center gap-2">
                                <Button asChild variant="ghost">
                                    <Link href="/login">Log in</Link>
                                </Button>
                                <Button asChild>
                                    <Link href="/register">Register</Link>
                                </Button>
                            </div>
                        )} */}
                    </div>

                    {/* Mobile Menu Button */}
                    <div className="lg:hidden">
                        <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
                            <SheetTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-10 w-10 text-gray-700 hover:bg-gray-50 hover:text-primary">
                                    <Menu className="h-6 w-6" />
                                    <span className="sr-only">Open menu</span>
                                </Button>
                            </SheetTrigger>
                            <SheetContent side="right" className="w-[300px] p-0 sm:w-[350px]">
                                <MobileNavigationContent onClose={() => setMobileMenuOpen(false)} />
                            </SheetContent>
                        </Sheet>
                    </div>
                </div>
            </div>
        </nav>
    );
}

// ListItem component for desktop navigation dropdowns
function ListItem({
    title,
    children,
    href,
    icon: Icon,
    ...props
}: React.ComponentPropsWithoutRef<'li'> & {
    href: string;
    icon?: React.ComponentType<{ className?: string }> | null;
}) {
    return (
        <li {...props}>
            <NavigationMenuLink asChild>
                <Link
                    href={href}
                    className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground block space-y-1 rounded-md p-3 leading-none no-underline transition-colors outline-none select-none"
                >
                    <div className="flex items-center space-x-2">
                        {Icon && <Icon className="h-4 w-4 text-primary" />}
                        <div className="text-sm leading-none font-medium">{title}</div>
                    </div>
                    {children && <p className="text-muted-foreground line-clamp-2 text-sm leading-snug">{children}</p>}
                </Link>
            </NavigationMenuLink>
        </li>
    );
}

interface MobileNavigationContentProps {
    onClose: () => void;
}

function MobileNavigationContent({ onClose }: MobileNavigationContentProps) {
    const { auth } = usePage<SharedData>().props;
    const [expandedItems, setExpandedItems] = useState<string[]>([]);

    const toggleExpanded = (title: string) => {
        setExpandedItems((prev) => (prev.includes(title) ? prev.filter((item) => item !== title) : [...prev, title]));
    };

    const handleLinkClick = () => {
        onClose();
    };

    return (
        <div className="flex h-full flex-col">
            {/* Header */}
            <div className="flex items-center justify-between border-b p-6">
                <h2 className="text-lg font-semibold">Menu</h2>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-6">
                {/* Main Navigation */}
                <div className="space-y-2">
                    {mainNavigation.map((item) => (
                        <div key={item.title}>
                            {item.children ? (
                                <div className="space-y-1">
                                    <button
                                        onClick={() => toggleExpanded(item.title)}
                                        className="hover:bg-accent flex w-full items-center justify-between rounded-lg p-3 text-left transition-colors"
                                    >
                                        <div className="flex items-center space-x-2">
                                            {item.icon && <item.icon className="h-4 w-4 text-primary" />}
                                            <span className="font-medium">{item.title}</span>
                                        </div>
                                        <ChevronDown
                                            className={cn('h-4 w-4 transition-transform', expandedItems.includes(item.title) && 'rotate-180')}
                                        />
                                    </button>
                                    {expandedItems.includes(item.title) && (
                                        <div className="ml-6 space-y-1">
                                            {item.children.map((child) => (
                                                <Link
                                                    key={child.title}
                                                    href={child.href}
                                                    onClick={handleLinkClick}
                                                    className="hover:bg-accent flex items-start space-x-3 rounded-md p-2 transition-colors"
                                                >
                                                    {child.icon && <child.icon className="mt-0.5 h-4 w-4 flex-shrink-0 text-primary" />}
                                                    <div className="min-w-0 flex-1">
                                                        <div className="text-sm font-medium">{child.title}</div>
                                                        {child.description && (
                                                            <div className="text-muted-foreground mt-0.5 text-xs">{child.description}</div>
                                                        )}
                                                    </div>
                                                </Link>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <Link
                                    href={item.href}
                                    onClick={handleLinkClick}
                                    className="hover:bg-accent flex items-center space-x-3 rounded-lg p-3 transition-colors"
                                >
                                    {item.icon && <item.icon className="h-4 w-4 flex-shrink-0 text-primary" />}
                                    <span className="font-medium">{item.title}</span>
                                </Link>
                            )}
                        </div>
                    ))}
                </div>

                {/* Right Navigation */}
                <div className="mt-6 space-y-2 border-t pt-6">
                    {rightNavigation.map((item) => (
                        <Link
                            key={item.title}
                            href={item.href}
                            onClick={handleLinkClick}
                            className="hover:bg-accent flex items-center space-x-3 rounded-lg p-3 transition-colors"
                        >
                            {item.icon && <item.icon className="h-4 w-4 flex-shrink-0 text-primary" />}
                            <span className="font-medium">{item.title}</span>
                        </Link>
                    ))}
                </div>

                {/* Auth Navigation */}
                <div className="mt-6 space-y-3 border-t pt-6">
                    {auth.user ? (
                        <Button asChild className="w-full">
                            <Link href="/dashboard" onClick={handleLinkClick}>
                                Dashboard
                            </Link>
                        </Button>
                    ) : (
                        <div className="space-y-2">
                            <Button asChild variant="outline" className="w-full">
                                <Link href="/login" onClick={handleLinkClick}>
                                    Log in
                                </Link>
                            </Button>
                            <Button asChild className="w-full">
                                <Link href="/register" onClick={handleLinkClick}>
                                    Register
                                </Link>
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
