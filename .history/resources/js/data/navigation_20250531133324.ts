import { type NavItem } from '@/types';
import {
    Shield,
    Settings,
    FileText,
    MessageSquare,
    Lock,
    DollarSign,
    Users,
    Building,
    CreditCard,
    FileCheck,
    BookOpen,
    Calendar,
    Phone,
    LogIn,
    UserPlus,
    FileBarChart,
    UserCheck,
    Award,
    HelpCircle,
    Search,
    ClipboardList,
    Briefcase,
    FileX,
    Scale
} from 'lucide-react';

// Main navigation items for the public website
export const mainNavigation: NavItem[] = [
    {
        title: 'Services',
        href: '/services',
        icon: Settings,
        children: [
            {
                title: 'Insurance',
                href: '/services/insurance',
                icon: Shield,
                description: 'Comprehensive insurance solutions'
            },
            {
                title: 'Finance & Accounting',
                href: '/services/finance-accounting',
                icon: CreditCard,
                description: 'Financial management services'
            },
            {
                title: 'HR Management',
                href: '/services/hr-management',
                icon: Users,
                description: 'Human resources solutions'
            },
            {
                title: 'Compliance and Admin',
                href: '/services/compliance-admin',
                icon: FileCheck,
                description: 'Regulatory compliance support'
            }
        ]
    },
    {
        title: 'Solutions',
        href: '/solutions',
        icon: Briefcase,
        children: [
            {
                title: 'Insight & Resource',
                href: '/solutions/insight-resource',
                icon: BookOpen,
                description: 'Business insights and resources'
            },
            {
                title: 'Category-Wise List',
                href: '/solutions/category-wise',
                icon: ClipboardList,
                description: 'Organized solution categories'
            }
        ]
    },
    {
        title: 'Case Studies',
        href: '/case-studies',
        icon: FileBarChart,
        description: 'Success stories and case studies'
    },
    {
        title: 'Blog',
        href: '/blog',
        icon: MessageSquare,
        description: 'Latest insights and updates'
    },
    {
        title: 'Data Security',
        href: '/data-security',
        icon: Lock,
        description: 'Security measures and compliance'
    },
    {
        title: 'Pricing',
        href: '/pricing',
        icon: DollarSign,
        description: 'Transparent pricing plans'
    },
    {
        title: 'About Us',
        href: '/about',
        icon: Building,
        children: [
            {
                title: 'About Us',
                href: '/about',
                icon: Building,
                description: 'Our company story'
            },
            {
                title: 'Team',
                href: '/about/team',
                icon: Users,
                description: 'Meet our team'
            },
            {
                title: 'Careers',
                href: '/about/careers',
                icon: Briefcase,
                description: 'Join our team'
            },
            {
                title: 'Testimonials',
                href: '/about/testimonials',
                icon: UserCheck,
                description: 'Client testimonials'
            },
            {
                title: 'FAQ',
                href: '/about/faq',
                icon: HelpCircle,
                description: 'Frequently asked questions'
            },
            {
                title: 'General Inquiry',
                href: '/about/inquiry',
                icon: Search,
                description: 'General inquiries'
            },
            {
                title: 'Schedule a Meeting',
                href: '/about/schedule',
                icon: Calendar,
                description: 'Book a consultation'
            },
            {
                title: 'Service Intake Form',
                href: '/about/service-intake',
                icon: FileText,
                description: 'Service request form'
            }
        ]
    }
];

// Right side navigation items
export const rightNavigation: NavItem[] = [

];

// Authentication navigation items
export const authNavigation: NavItem[] = [
    {
        title: 'Login',
        href: '/login',
        icon: LogIn,
        description: 'Sign in to your account'
    },
    {
        title: 'Forgot Password',
        href: '/forgot-password',
        icon: FileX,
        description: 'Reset your password'
    },
    {
        title: 'Sign Up',
        href: '/register',
        icon: UserPlus,
        description: 'Create a new account'
    },
    {
        title: 'Terms & Conditions',
        href: '/terms',
        icon: Scale,
        description: 'Terms and conditions'
    }
];

// Mobile navigation - flattened structure for easier mobile navigation
export const mobileNavigation: NavItem[] = [
    ...mainNavigation,
    ...rightNavigation,
    ...authNavigation
];
