import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { CustomFlowbiteTheme, createTheme } from 'flowbite-react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
    theme: Theme;
    toggleTheme: () => void;
    flowbiteTheme: CustomFlowbiteTheme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const lightTheme = createTheme({
    button: {
        color: {
            primary: "bg-red-500 hover:bg-red-600 text-white",
            secondary: "bg-blue-500 hover:bg-blue-600 text-white",
        },
        size: {
            lg: "px-6 py-3 text-lg",
        },
    },
});

const darkTheme = createTheme({
    button: {
        color: {
            primary: "bg-red-700 hover:bg-red-800 text-white",
            secondary: "bg-blue-700 hover:bg-blue-800 text-white",
        },
        size: {
            lg: "px-6 py-3 text-lg",
        },
    },
});

export function ThemeProvider({ children }: { children: ReactNode }) {
    const [theme, setTheme] = useState<Theme>('light');

    useEffect(() => {
        // Check for saved theme preference or system preference
        const savedTheme = localStorage.getItem('theme') as Theme;
        if (savedTheme) {
            setTheme(savedTheme);
            document.documentElement.classList.toggle('dark', savedTheme === 'dark');
        }
    }, []);

    const toggleTheme = () => {
        const newTheme = theme === 'light' ? 'dark' : 'light';
        setTheme(newTheme);
        localStorage.setItem('theme', newTheme);
        document.documentElement.classList.toggle('dark', newTheme === 'dark');
    };

    const flowbiteTheme = theme === 'light' ? lightTheme : darkTheme;

    return (
        <ThemeContext.Provider value={{ theme, toggleTheme, flowbiteTheme }}>
            {children}
        </ThemeContext.Provider>
    );
}

export function useTheme() {
    const context = useContext(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
