import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Users,
    Zap,
    Shield,
    CheckCircle
} from 'lucide-react';

interface Service {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    features: string[];
    gradient: string;
}

const services: Service[] = [
    {
        icon: Users,
        title: 'Dedicated Team',
        description: 'Boost your business performance with our skilled, full-time teams. Get your own dedicated team of professionals working exclusively on your projects with full commitment and focus.',
        features: [
            'Exclusive resources for your projects',
            'Customized team structure',
            'Long-term collaboration'
        ],
        gradient: 'from-blue-500 to-blue-600'
    },
    {
        icon: Zap,
        title: 'On-Demand Service Support',
        description: 'Get expert help when you need it without the overhead. Flexible support services available whenever you need them, scaling up or down based on your requirements.',
        features: [
            'Pay-as-you-go model',
            '24/7 availability',
            'Quick response times'
        ],
        gradient: 'from-green-500 to-green-600'
    },
    {
        icon: Shield,
        title: 'Business Care Plans',
        description: 'We manage the back office, so you can focus on growth. Comprehensive care packages designed to maintain and optimize your business operations and infrastructure.',
        features: [
            'Regular maintenance',
            'Proactive monitoring',
            'Priority support'
        ],
        gradient: 'from-purple-500 to-purple-600'
    }
];

export function ProfessionalServicesSection() {
    return (
        <section className="py-16 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Professional Services</h2>
                    <p className="text-xl text-gray-600">Tailored solutions to meet your business needs and drive growth</p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {services.map((service, index) => (
                        <ServiceCard key={service.title} service={service} index={index} />
                    ))}
                </div>
            </div>
        </section>
    );
}

interface ServiceCardProps {
    service: Service;
    index: number;
}

function ServiceCard({ service, index }: ServiceCardProps) {
    const Icon = service.icon;
    
    return (
        <Card className="group hover:shadow-xl transition-all duration-300 bg-white border-gray-200 hover:border-blue-200">
            <CardHeader>
                <div className={`w-16 h-16 bg-gradient-to-br ${service.gradient} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform`}>
                    <Icon className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-2xl mb-4 text-gray-900">{service.title}</CardTitle>
                <CardDescription className="text-gray-600 text-base leading-relaxed">
                    {service.description}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <ul className="space-y-3">
                    {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-gray-600">
                            <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                            {feature}
                        </li>
                    ))}
                </ul>
            </CardContent>
        </Card>
    );
}
