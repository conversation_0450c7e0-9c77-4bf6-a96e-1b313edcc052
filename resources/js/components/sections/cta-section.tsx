import { Link } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';

interface CtaSectionProps {
    title?: string;
    description?: string;
    buttonText?: string;
    buttonHref?: string;
    className?: string;
}

export function CtaSection({
    title = "Ready to Scale Smarter?",
    description = "Let's create a custom support model for your business.",
    buttonText = "Talk to Our Team",
    buttonHref = "/contact",
    className = ""
}: CtaSectionProps) {
    return (
        <section className={`py-16 bg-gradient-to-br from-blue-600 to-blue-700 text-white ${className}`}>
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 className="text-3xl md:text-4xl font-bold mb-6">{title}</h2>
                <p className="text-xl mb-8 text-blue-100">{description}</p>
                <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3" asChild>
                    <Link href={buttonHref}>
                        {buttonText}
                        <ArrowRight className="ml-2 h-5 w-5" />
                    </Link>
                </Button>
            </div>
        </section>
    );
}
