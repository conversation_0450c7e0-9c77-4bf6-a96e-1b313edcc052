import {
    BarChart3,
    <PERSON>ting<PERSON>,
    Zap,
    TrendingUp
} from 'lucide-react';

interface ExpertiseItem {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    gradient: string;
}

const expertiseItems: ExpertiseItem[] = [
    {
        icon: BarChart3,
        title: 'Assessment of Requirements',
        description: 'Our systematic approach to understanding your business needs ensures we deliver precisely what you need through detailed analysis and collaborative sessions.',
        gradient: 'from-blue-500 to-blue-600'
    },
    {
        icon: Settings,
        title: 'Process Mapping',
        description: 'We document and visualize your operational workflows, identifying optimization opportunities and streamlining critical processes.',
        gradient: 'from-green-500 to-green-600'
    },
    {
        icon: Zap,
        title: 'Workflow Automation',
        description: 'Streamline operational tasks through smart automation—reducing effort, improving accuracy, and enhancing your team\'s impact.',
        gradient: 'from-purple-500 to-purple-600'
    },
    {
        icon: TrendingUp,
        title: 'Performance Monitoring',
        description: 'We track key metrics and provide regular insights, helping you make data-driven decisions and continuously improve operations.',
        gradient: 'from-red-500 to-red-600'
    }
];

export function ExpertiseSection() {
    return (
        <section className="py-16 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Leverage Our Expertise</h2>
                    <p className="text-xl text-gray-600">Transform your operations with our proven methodologies</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {expertiseItems.map((item, index) => (
                        <ExpertiseCard key={item.title} item={item} index={index} />
                    ))}
                </div>
            </div>
        </section>
    );
}

interface ExpertiseCardProps {
    item: ExpertiseItem;
    index: number;
}

function ExpertiseCard({ item, index }: ExpertiseCardProps) {
    const Icon = item.icon;
    
    return (
        <div className="text-center group">
            <div className={`w-16 h-16 bg-gradient-to-br ${item.gradient} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform`}>
                <Icon className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">{item.title}</h3>
            <p className="text-gray-600 leading-relaxed">
                {item.description}
            </p>
        </div>
    );
}
