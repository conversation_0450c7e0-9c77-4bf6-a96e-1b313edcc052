import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Globe,
    TrendingUp,
    DollarSign,
    Shield
} from 'lucide-react';

interface Strength {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    gradient: string;
}

const strengths: Strength[] = [
    {
        icon: Globe,
        title: 'Global Expertise',
        description: 'Our team spans 20+ countries, bringing diverse perspectives and local market knowledge to every project.',
        gradient: 'from-blue-500 to-blue-600'
    },
    {
        icon: TrendingUp,
        title: 'Scalable Solutions',
        description: 'Architectures designed to grow with your business, from startup to enterprise scale.',
        gradient: 'from-green-500 to-green-600'
    },
    {
        icon: DollarSign,
        title: 'Cost Efficiency',
        description: 'Optimized solutions that maximize your ROI without compromising on quality or performance.',
        gradient: 'from-purple-500 to-purple-600'
    },
    {
        icon: Shield,
        title: 'Reliability & Security',
        description: 'Enterprise-grade security and 99.99% uptime ensure your operations never skip a beat.',
        gradient: 'from-red-500 to-red-600'
    }
];

export function CoreStrengthsSection() {
    return (
        <section className="py-16 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Core Strengths</h2>
                    <p className="text-xl text-gray-600">Delivering exceptional value through our key capabilities</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {strengths.map((strength, index) => (
                        <StrengthCard key={strength.title} strength={strength} index={index} />
                    ))}
                </div>
            </div>
        </section>
    );
}

interface StrengthCardProps {
    strength: Strength;
    index: number;
}

function StrengthCard({ strength, index }: StrengthCardProps) {
    const Icon = strength.icon;
    
    return (
        <Card className="text-center group hover:shadow-lg transition-all duration-300 border-gray-200 hover:border-blue-200">
            <CardHeader>
                <div className={`w-16 h-16 bg-gradient-to-br ${strength.gradient} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform`}>
                    <Icon className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl mb-4 text-gray-900">{strength.title}</CardTitle>
                <CardDescription className="text-gray-600 leading-relaxed">
                    {strength.description}
                </CardDescription>
            </CardHeader>
        </Card>
    );
}
